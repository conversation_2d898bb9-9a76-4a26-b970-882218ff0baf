# KaziSync Design System

## Overview

This design system provides a comprehensive set of reusable components, design tokens, and guidelines to ensure consistency across the KaziSync application.

## Design Principles

### 1. Accessibility First
- WCAG 2.1 AA compliance
- Proper semantic HTML
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast ratios

### 2. Mobile-First Responsive Design
- Touch-friendly interfaces (44px minimum touch targets)
- Progressive enhancement
- Flexible layouts that work across devices

### 3. Consistent Visual Language
- Unified color palette
- Consistent typography scale
- Standardized spacing system
- No box-shadows (replaced with borders)

## Color System

### Primary Colors
- `primary-50`: #E3F2FD (Light backgrounds)
- `primary-500`: #2196F3 (Default primary)
- `primary-700`: #1976D2 (Primary dark)
- `primary-900`: #0D47A1 (Darkest primary)

### Secondary Colors
- `secondary-500`: #757575 (Default secondary)
- `secondary-700`: #424242 (Secondary dark)
- `secondary-900`: #212121 (Darkest secondary)

### Semantic Colors
- `success`: #2E7D32 (Success states)
- `danger`: #D32F2F (Error states)
- `warning`: #F57C00 (Warning states)
- `info`: #0288D1 (Information states)

### Text Colors
- `text-primary`: #212121 (Primary text)
- `text-secondary`: #757575 (Secondary text)
- `text-disabled`: #BDBDBD (Disabled text)

## Typography

### Font Stack
- Primary: Roboto, Arial, sans-serif
- Optimized for readability and accessibility

### Scale
- `text-xs`: 0.75rem (12px)
- `text-sm`: 0.875rem (14px)
- `text-base`: 1rem (16px)
- `text-lg`: 1.125rem (18px)
- `text-xl`: 1.25rem (20px)
- `text-2xl`: 1.5rem (24px)
- `text-3xl`: 1.875rem (30px)
- `text-4xl`: 2.25rem (36px)

## Components

### Button
Standardized button component with multiple variants and states.

```tsx
import { Button } from '@/components/ui/Button';

// Basic usage
<Button>Click me</Button>

// With variants
<Button variant="primary">Primary</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="danger">Danger</Button>
<Button variant="success">Success</Button>

// With sizes
<Button size="sm">Small</Button>
<Button size="md">Medium</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>

// With loading state
<Button loading>Loading...</Button>

// With icons
<Button leftIcon={<PlusIcon />}>Add Item</Button>
<Button rightIcon={<ArrowIcon />}>Next</Button>
```

### Input
Accessible input component with validation states.

```tsx
import { Input } from '@/components/ui/Input';

// Basic usage
<Input placeholder="Enter text" />

// With label and validation
<Input
  label="Email Address"
  type="email"
  required
  error="Please enter a valid email"
/>

// With success state
<Input
  label="Username"
  success="Username is available"
/>

// With icons
<Input
  leftIcon={<SearchIcon />}
  placeholder="Search..."
/>
```

### Modal
Accessible modal component with keyboard navigation.

```tsx
import { Modal } from '@/components/ui/Modal';

<Modal
  isOpen={isOpen}
  onClose={onClose}
  title="Modal Title"
  description="Modal description"
  size="md"
>
  <p>Modal content goes here</p>
</Modal>
```

### DashboardCard
Reusable card component for dashboard layouts.

```tsx
import DashboardCard from '@/components/ui/DashboardCard';

<DashboardCard title="Card Title" loading={false}>
  <p>Card content</p>
</DashboardCard>
```

### DashboardStats
Statistics display component with accessibility features.

```tsx
import DashboardStats from '@/components/ui/DashboardStats';

<DashboardStats
  title="Total Users"
  value="1,234"
  change="+12%"
  changeType="positive"
/>
```

## Layout Guidelines

### Spacing System
- Use consistent spacing values: 4, 8, 12, 16, 20, 24, 32, 40, 48, 64px
- Apply spacing using Tailwind classes: `p-4`, `m-6`, `gap-4`

### Grid System
- Use CSS Grid and Flexbox for layouts
- Responsive breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)

### Touch Targets
- Minimum 44px height for interactive elements
- Adequate spacing between clickable elements

## Accessibility Guidelines

### Semantic HTML
- Use proper heading hierarchy (h1 → h6)
- Include landmark elements (header, nav, main, aside, footer)
- Use lists for grouped content

### ARIA Labels
- Provide descriptive aria-labels for interactive elements
- Use aria-describedby for form validation messages
- Include role attributes where appropriate

### Keyboard Navigation
- Ensure all interactive elements are keyboard accessible
- Provide visible focus indicators
- Implement logical tab order

### Color Contrast
- Text on background: minimum 4.5:1 ratio
- Large text: minimum 3:1 ratio
- Interactive elements: sufficient contrast in all states

## Best Practices

### Component Development
1. Start with semantic HTML
2. Add accessibility attributes
3. Implement responsive design
4. Test with keyboard navigation
5. Verify color contrast
6. Test with screen readers

### CSS Guidelines
1. Use Tailwind utility classes
2. Avoid custom CSS when possible
3. No box-shadows (use borders instead)
4. Consistent hover and focus states
5. Smooth transitions for interactive elements

### Performance
1. Optimize images and assets
2. Use semantic HTML for better SEO
3. Minimize custom CSS
4. Leverage Tailwind's purging for smaller bundles

## Testing Checklist

### Accessibility Testing
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets WCAG standards
- [ ] Focus indicators are visible
- [ ] Form validation is accessible

### Responsive Testing
- [ ] Mobile devices (320px+)
- [ ] Tablets (768px+)
- [ ] Desktop (1024px+)
- [ ] Touch targets are adequate
- [ ] Text is readable at all sizes

### Browser Testing
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers
