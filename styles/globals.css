@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply font-sans;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-text-primary;
    font-feature-settings: 'kern' 1, 'liga' 1;
    text-rendering: optimizeLegibility;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold text-text-primary;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-4xl md:text-5xl leading-tight;
    font-weight: 800;
  }

  h2 {
    @apply text-3xl md:text-4xl leading-tight;
    font-weight: 700;
  }

  h3 {
    @apply text-2xl md:text-3xl leading-snug;
    font-weight: 600;
  }

  h4 {
    @apply text-xl md:text-2xl leading-snug;
    font-weight: 600;
  }

  h5 {
    @apply text-lg md:text-xl leading-normal;
    font-weight: 500;
  }

  h6 {
    @apply text-base md:text-lg leading-normal;
    font-weight: 500;
  }

  p {
    @apply text-text-secondary leading-relaxed;
  }
  
  a {
    @apply text-primary hover:text-primary-dark transition-colors duration-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    min-height: 44px; /* WCAG touch target size */
  }

  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-dark hover:text-white focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-dark hover:text-white focus:ring-secondary-500 shadow-sm hover:shadow-md;
  }

  .btn-outline {
    @apply btn border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary-500 bg-white;
  }

  .btn-ghost {
    @apply btn text-text-primary hover:bg-gray-100 focus:ring-gray-500 bg-transparent;
  }

  .btn-danger {
    @apply btn bg-danger text-white hover:bg-danger-600 focus:ring-danger-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply btn bg-success text-white hover:bg-success-600 focus:ring-success-500 shadow-sm hover:shadow-md;
  }
  
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section {
    @apply py-12 md:py-16 lg:py-20;
  }
  
  .card {
    @apply bg-white rounded-xl border border-gray-200 overflow-hidden;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
