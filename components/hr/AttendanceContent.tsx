'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';
import ManualAttendanceModal from '@/components/attendance/ManualAttendanceModal';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import AttendanceDetailsModal from '@/components/attendance/AttendanceDetailsModal';

// Define attendance record interface
interface AttendanceRecord {
  attendance_id: string;
  check_in_time: string | null;
  check_out_time: string | null;
  created_at: string;
  created_by: string | null;
  date: string;
  employee_id: string;
  expected_end_time: string | null;
  expected_start_time: string | null;
  is_overtime: boolean | null;
  notes: string | null;
  overtime_hours: number | null;
  shift_id: string | null;
  source: string;
  source_record_id: string | null;
  status: string;
  total_hours: number | null;
  updated_at: string;
}

// Define employee interface
interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

const AttendanceContent: React.FC = () => {
  const { companies } = useAuth();
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [selectedEmployee, setSelectedEmployee] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [singleDate, setSingleDate] = useState<Date | null>(new Date());
  const [filterType, setFilterType] = useState<'date-range' | 'employee' | 'single-date'>('date-range');

  // Modal states
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isManualEntryModalOpen, setIsManualEntryModalOpen] = useState(false);
  const [selectedAttendanceId, setSelectedAttendanceId] = useState<string | null>(null);
  const [selectedEmployeeName, setSelectedEmployeeName] = useState('');

  // Fetch employees on component mount
  useEffect(() => {
    fetchEmployees();
  }, [companies]);

  // Fetch attendance records when filter changes
  useEffect(() => {
    if (companies && companies.length > 0) {
      fetchAttendanceRecords();
    }
  }, [companies, selectedEmployee, startDate, endDate, singleDate, filterType]);

  // Function to fetch employees
  const fetchEmployees = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {employees: Employee[]}, msg: string}>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch employees');
    }
  };

  // Function to fetch attendance records
  const fetchAttendanceRecords = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let endpoint = `api/attendance?company_id=${companyId}`;

      // Add filter parameters based on selected filter type
      if (filterType === 'date-range' && startDate && endDate) {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        endpoint += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
      } else if (filterType === 'employee' && selectedEmployee) {
        endpoint += `&employee_id=${selectedEmployee}`;
      } else if (filterType === 'single-date' && singleDate) {
        const formattedDate = singleDate.toISOString().split('T')[0];
        endpoint += `&date=${formattedDate}`;
      }

      const response = await apiGet<{attendance_records: AttendanceRecord[], status: string}>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.attendance_records) {
        setAttendanceRecords(response.attendance_records);
      } else {
        setAttendanceRecords([]);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch attendance records');
      setAttendanceRecords([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Format time for display
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A';

    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return timeString;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Format hours for display
  const formatHours = (hours: number | null) => {
    if (hours === null) return 'N/A';

    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    return `${wholeHours}h ${minutes}m`;
  };

  // Get employee name by ID
  const getEmployeeName = (employeeId: string) => {
    const employee = employees.find(emp => emp.employee_id === employeeId);
    return employee ? employee.full_name : 'Unknown Employee';
  };

  // Open attendance details modal
  const openDetailsModal = (attendanceId: string, employeeId: string) => {
    const employeeName = getEmployeeName(employeeId);
    setSelectedAttendanceId(attendanceId);
    setSelectedEmployeeName(employeeName);
    setIsDetailsModalOpen(true);
  };

  // Close attendance details modal
  const closeDetailsModal = () => {
    setSelectedAttendanceId(null);
    setIsDetailsModalOpen(false);
  };

  // Open manual entry modal
  const openManualEntryModal = () => {
    setIsManualEntryModalOpen(true);
  };

  // Close manual entry modal
  const closeManualEntryModal = () => {
    setIsManualEntryModalOpen(false);
  };

  // Handle successful manual entry
  const handleManualEntrySuccess = () => {
    fetchAttendanceRecords();
  };

  return (
    <div className="space-y-6">
      {/* Attendance Details Modal */}
      <AttendanceDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={closeDetailsModal}
        attendanceId={selectedAttendanceId}
        employeeName={selectedEmployeeName}
      />

      {/* Manual Attendance Entry Modal */}
      <ManualAttendanceModal
        isOpen={isManualEntryModalOpen}
        onClose={closeManualEntryModal}
        onSuccess={handleManualEntrySuccess}
      />

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Attendance Management</h1>
        <div className="flex space-x-3">
          <Link
            href="/dashboard/hr/attendance/daily"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>Daily Report</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance/dashboard"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Statistics</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance/employee"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span>Employee Stats</span>
          </Link>
          <button
            onClick={openManualEntryModal}
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-primary rounded-md border border-primary hover:bg-primary-dark hover:border-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Manual Entry</span>
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Attendance</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <DashboardCard title="Attendance Filters">
        <div className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="col-span-1 sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-secondary-dark mb-1">
                Filter Type
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="date-range">Date Range</option>
                <option value="employee">By Employee</option>
                <option value="single-date">Single Date</option>
              </select>
            </div>

            {filterType === 'date-range' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-secondary-dark mb-1">
                    Start Date
                  </label>
                  <DatePicker
                    selected={startDate}
                    onChange={(date) => setStartDate(date)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    dateFormat="yyyy-MM-dd"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-dark mb-1">
                    End Date
                  </label>
                  <DatePicker
                    selected={endDate}
                    onChange={(date) => setEndDate(date)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    dateFormat="yyyy-MM-dd"
                    minDate={startDate || undefined}
                  />
                </div>
              </>
            )}

            {filterType === 'employee' && (
              <div className="col-span-1 sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Select Employee
                </label>
                <select
                  value={selectedEmployee}
                  onChange={(e) => setSelectedEmployee(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">All Employees</option>
                  {employees.map((employee) => (
                    <option key={employee.employee_id} value={employee.employee_id}>
                      {employee.full_name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {filterType === 'single-date' && (
              <div className="col-span-1 sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Select Date
                </label>
                <DatePicker
                  selected={singleDate}
                  onChange={(date) => setSingleDate(date)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  dateFormat="yyyy-MM-dd"
                />
              </div>
            )}
          </div>
        </div>
      </DashboardCard>

      <DashboardCard title="Attendance Records">
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading attendance records...</p>
          </div>
        ) : attendanceRecords.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No attendance records found for the selected filters.</p>
          </div>
        ) : (
          <div className="relative flex flex-col h-[70vh] md:h-[60vh]">
            {/* Scrollable Table Container */}
            <div className="overflow-y-auto overflow-x-auto flex-1">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check Out
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Hours
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Source
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {attendanceRecords.map((record) => (
                    <tr key={record.attendance_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-secondary-dark">
                          {getEmployeeName(record.employee_id)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatDate(record.date)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatTime(record.check_in_time)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatTime(record.check_out_time)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{formatHours(record.total_hours)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          record.status === 'present'
                            ? 'bg-green-100 text-green-800'
                            : record.status === 'absent'
                            ? 'bg-red-100 text-red-800'
                            : record.status === 'late'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{record.source}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex flex-col md:flex-row md:justify-end space-y-2 md:space-y-0 md:space-x-3">
                          <button
                            className="text-primary hover:text-primary-dark"
                            onClick={() => openDetailsModal(record.attendance_id, record.employee_id)}
                          >
                            View
                          </button>
                          <Link
                            href={`/dashboard/hr/attendance/employee/${record.employee_id}`}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            Stats
                          </Link>
                          <button
                            className="text-secondary-dark hover:text-secondary"
                            onClick={() => {/* Edit record */}}
                          >
                            Edit
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default AttendanceContent;
