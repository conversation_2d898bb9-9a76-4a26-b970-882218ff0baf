'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import Link from 'next/link';

interface Shift {
  shift_id: string;
  company_id: string;
  name: string;
  start_time: string;
  end_time: string;
  break_duration: number;
  break_start_time: string;
  working_days: string;
  description: string;
  is_night_shift: boolean;
  is_flexible: boolean;
  grace_period_late: number;
  grace_period_early: number;
  created_at: string;
  updated_at: string;
}

interface ShiftAssignment {
  assignment_id: string;
  employee_id: string;
  shift_id: string;
  effective_start_date: string;
  effective_end_date: string;
  custom_start_time: string;
  custom_end_time: string;
  custom_break_duration: number;
  custom_working_days: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  shift: Shift;
}

interface ExtendedUser {
  employee_id: string;
  employee_info: {
    created_at: string;
    department_id: string;
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string | null;
    last_name: string;
    phone_number: string | null;
    position: string | null;
    status: string;
    updated_at: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

const EmployeeShiftsContent: React.FC = () => {
  const { user, companies } = useAuth();
  const extendedUser = user as ExtendedUser;
  const [assignments, setAssignments] = useState<ShiftAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentWeekShifts, setCurrentWeekShifts] = useState<any[]>([]);

  // Fetch shift assignments on component mount
  useEffect(() => {
    fetchShiftAssignments();
  }, [companies, extendedUser?.employee_id, extendedUser?.employee_info?.employee_id]);

  // Get company ID using the same robust method
  const getCompanyId = () => {
    // First try to get from companies array
    if (companies && companies.length > 0) {
      return companies[0].company_id;
    }

    // If companies array is empty, try to get from stored auth data
    const authData = localStorage.getItem('kazisync_auth');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        // Check if company object exists (from login response)
        if (parsedData.company && parsedData.company.company_id) {
          return parsedData.company.company_id;
        }
        // Also check if company_id is directly in the token payload
        else if (parsedData.company_id) {
          return parsedData.company_id;
        }
      } catch (error) {
        // Silently handle parsing errors
      }
    }
    return null;
  };

  // Function to fetch shift assignments
  const fetchShiftAssignments = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Get employee ID from user object - now we know the correct structure
      let employeeId = null;
      if (extendedUser?.employee_id) {
        employeeId = extendedUser.employee_id;
      } else if (extendedUser?.employee_info?.employee_id) {
        employeeId = extendedUser.employee_info.employee_id;
      }

      if (!employeeId) {
        setError('Employee ID not found. Please contact support.');
        setIsLoading(false);
        return;
      }

      const companyId = getCompanyId();
      const token = getAccessToken();

      if (!token || !companyId) {
        throw new Error('Authentication or company information not available');
      }

      const response = await apiGet<{
        code: number;
        extend: {
          assignments: ShiftAssignment[];
          pagination: {
            has_next: boolean;
            has_prev: boolean;
            page: number;
            pages: number;
            per_page: number;
            total_count: number;
          };
        };
        msg: string;
      }>(`api/employee-shifts?company_id=${companyId}&employee_id=${employeeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.extend && response.extend.assignments) {
        setAssignments(response.extend.assignments);
        generateWeeklySchedule(response.extend.assignments);
      } else {
        setAssignments([]);
        setCurrentWeekShifts([]);
      }
    } catch (error: any) {
      console.error('Error fetching shift assignments:', error);
      setError(error.message || 'Failed to fetch shift assignments');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate weekly schedule
  const generateWeeklySchedule = (assignments: ShiftAssignment[]) => {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Start from Sunday

    const weekSchedule = [];
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startOfWeek);
      currentDate.setDate(startOfWeek.getDate() + i);

      const dayOfWeek = currentDate.getDay();
      const activeAssignment = assignments.find(assignment => {
        const startDate = new Date(assignment.effective_start_date);
        const endDate = new Date(assignment.effective_end_date);
        const workingDays = assignment.custom_working_days.split(',').map(d => parseInt(d));

        return assignment.is_active &&
               currentDate >= startDate &&
               currentDate <= endDate &&
               workingDays.includes(dayOfWeek);
      });

      weekSchedule.push({
        date: currentDate,
        dayName: currentDate.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: currentDate.getDate(),
        isToday: currentDate.toDateString() === today.toDateString(),
        assignment: activeAssignment
      });
    }
    setCurrentWeekShifts(weekSchedule);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  // Format time for display
  const formatTime = (timeString: string) => {
    try {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return timeString;
    }
  };

  // Format working days for display
  const formatWorkingDays = (daysString: string) => {
    const dayMap: {[key: string]: string} = {
      '0': 'Sun',
      '1': 'Mon',
      '2': 'Tue',
      '3': 'Wed',
      '4': 'Thu',
      '5': 'Fri',
      '6': 'Sat'
    };

    return daysString.split(',').map(day => dayMap[day] || day).join(', ');
  };

  // Calculate shift duration
  const calculateShiftDuration = (startTime: string, endTime: string, breakDuration: number) => {
    try {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      let totalMinutes = (endHours * 60 + endMinutes) - (startHours * 60 + startMinutes);

      // Handle overnight shifts
      if (totalMinutes < 0) {
        totalMinutes += 24 * 60;
      }

      // Subtract break duration
      totalMinutes -= breakDuration;

      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      return `${hours}h ${minutes}m`;
    } catch (error) {
      return 'N/A';
    }
  };

  // Check if a shift is active today
  const isActiveToday = (assignment: ShiftAssignment) => {
    const today = new Date();
    const startDate = new Date(assignment.effective_start_date);
    const endDate = new Date(assignment.effective_end_date);

    // Check if today is within the effective date range
    if (today < startDate || today > endDate) {
      return false;
    }

    // Check if today is a working day
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const workingDays = assignment.custom_working_days.split(',').map(d => parseInt(d));

    return workingDays.includes(dayOfWeek);
  };

  // Get today's active shift
  const todayShift = assignments.find(isActiveToday);

  // Calculate stats
  const activeAssignments = assignments.filter(a => a.is_active);
  const totalWorkingDays = activeAssignments.reduce((total, assignment) => {
    return total + assignment.custom_working_days.split(',').length;
  }, 0);

  const averageShiftDuration = activeAssignments.length > 0 ?
    activeAssignments.reduce((total, assignment) => {
      const duration = calculateShiftDuration(
        assignment.custom_start_time,
        assignment.custom_end_time,
        assignment.custom_break_duration
      );
      const hours = parseFloat(duration.split('h')[0]) || 0;
      return total + hours;
    }, 0) / activeAssignments.length : 0;

  const stats = [
    {
      title: 'Active Shifts',
      value: activeAssignments.length.toString(),
      change: '',
      changeType: 'positive' as const
    },
    {
      title: 'Working Days/Week',
      value: Math.round(totalWorkingDays / Math.max(activeAssignments.length, 1)).toString(),
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Avg. Shift Duration',
      value: `${averageShiftDuration.toFixed(1)}h`,
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Night Shifts',
      value: activeAssignments.filter(a => a.shift.is_night_shift).length.toString(),
      change: '',
      changeType: 'neutral' as const
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header with Breadcrumb Navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <Link href="/dashboard/employee" className="hover:text-blue-600">Dashboard</Link>
            <span>/</span>
            <span className="text-gray-900">My Shifts</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">My Shifts</h1>
          <p className="text-sm text-gray-600 mt-1">
            View your shift schedule and assignments
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Weekly Schedule */}
      <DashboardCard title="This Week's Schedule" loading={isLoading}>
        {currentWeekShifts.length > 0 ? (
          <div className="space-y-4">
            {/* Week Navigation Header */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Week of {currentWeekShifts[0]?.date.toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-1"></div>
                  Today
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                  Scheduled
                </span>
              </div>
            </div>

            {/* Enhanced Weekly Grid */}
            <div className="grid grid-cols-1 md:grid-cols-7 gap-3">
              {currentWeekShifts.map((day, index) => (
                <div
                  key={index}
                  className={`relative p-4 rounded-xl border-2 transition-all duration-200 ${
                    day.isToday
                      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100'
                      : day.assignment
                      ? 'border-green-200 bg-gradient-to-br from-green-50 to-green-100 hover:border-green-300'
                      : 'border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 hover:border-gray-300'
                  }`}
                >
                  {/* Today indicator */}
                  {day.isToday && (
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}

                  <div className="text-center">
                    {/* Day Header */}
                    <div className={`text-sm font-semibold mb-1 ${
                      day.isToday ? 'text-blue-700' : 'text-gray-700'
                    }`}>
                      {day.dayName}
                    </div>
                    <div className={`text-2xl font-bold mb-3 ${
                      day.isToday ? 'text-blue-600' : 'text-gray-900'
                    }`}>
                      {day.dayNumber}
                    </div>

                    {/* Shift Information */}
                    {day.assignment ? (
                      <div className="space-y-2">
                        {/* Shift Name */}
                        <div className={`text-sm font-semibold px-2 py-1 rounded-lg ${
                          day.assignment.shift.is_night_shift
                            ? 'bg-purple-200 text-purple-800'
                            : 'bg-green-200 text-green-800'
                        }`}>
                          {day.assignment.shift.name}
                        </div>

                        {/* Time Range */}
                        <div className="text-xs font-medium text-gray-700">
                          {formatTime(day.assignment.custom_start_time)}
                        </div>
                        <div className="text-xs text-gray-500">to</div>
                        <div className="text-xs font-medium text-gray-700">
                          {formatTime(day.assignment.custom_end_time)}
                        </div>

                        {/* Duration */}
                        <div className="text-xs text-gray-500 mt-2">
                          {calculateShiftDuration(
                            day.assignment.custom_start_time,
                            day.assignment.custom_end_time,
                            day.assignment.custom_break_duration
                          )}
                        </div>

                        {/* Shift Type Badges */}
                        <div className="flex flex-wrap justify-center gap-1 mt-2">
                          {day.assignment.shift.is_night_shift && (
                            <span className="inline-block px-1.5 py-0.5 bg-purple-100 text-purple-700 text-xs rounded-full">
                              🌙 Night
                            </span>
                          )}
                          {day.assignment.shift.is_flexible && (
                            <span className="inline-block px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full">
                              ⚡ Flexible
                            </span>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="w-12 h-12 mx-auto bg-gray-200 rounded-full flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                          </svg>
                        </div>
                        <div className="text-xs text-gray-400 font-medium">
                          No shift
                        </div>
                        <div className="text-xs text-gray-300">
                          Rest day
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="py-12 text-center">
            <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Schedule Available</h3>
            <p className="text-gray-500">No shifts scheduled for this week. Contact your manager for shift assignments.</p>
          </div>
        )}
      </DashboardCard>

      {/* Today's Shift Details */}
      {todayShift && (
        <DashboardCard title="Today's Shift Details">
          <div className={`rounded-xl p-6 ${
            todayShift.shift.is_night_shift
              ? 'bg-gradient-to-r from-purple-50 via-indigo-50 to-purple-50'
              : 'bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-50'
          }`}>
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-6">
                  <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                    todayShift.shift.is_night_shift
                      ? 'bg-gradient-to-br from-purple-500 to-purple-600'
                      : 'bg-gradient-to-br from-blue-500 to-blue-600'
                  }`}>
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      {todayShift.shift.is_night_shift ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      )}
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-2xl font-bold text-gray-900">
                        {todayShift.shift.name}
                      </h3>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        todayShift.shift.is_night_shift
                          ? 'bg-purple-100 text-purple-800 border border-purple-200'
                          : 'bg-green-100 text-green-800 border border-green-200'
                      }`}>
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          todayShift.shift.is_night_shift ? 'bg-purple-400' : 'bg-green-400'
                        }`}></div>
                        {todayShift.shift.is_night_shift ? 'Night Shift Active' : 'Active Today'}
                      </span>
                    </div>
                    <p className="text-gray-700 text-lg">
                      {todayShift.shift.description}
                    </p>

                    {/* Quick Time Display */}
                    <div className="mt-3 flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-lg font-semibold text-gray-900">
                          {formatTime(todayShift.custom_start_time)} - {formatTime(todayShift.custom_end_time)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span className="text-sm font-medium text-gray-700">
                          {calculateShiftDuration(
                            todayShift.custom_start_time,
                            todayShift.custom_end_time,
                            todayShift.custom_break_duration
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-white rounded-xl p-5 border border-gray-200 hover:border-gray-300 transition-colors">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Shift Time</div>
                    </div>
                    <div className="text-xl font-bold text-gray-900">
                      {formatTime(todayShift.custom_start_time)}
                    </div>
                    <div className="text-sm text-gray-500">
                      to {formatTime(todayShift.custom_end_time)}
                    </div>
                  </div>

                  <div className="bg-white rounded-xl p-5 border border-gray-200 hover:border-primary-300 transition-colors">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Duration</div>
                    </div>
                    <div className="text-xl font-bold text-gray-900">
                      {calculateShiftDuration(
                        todayShift.custom_start_time,
                        todayShift.custom_end_time,
                        todayShift.custom_break_duration
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      Working time
                    </div>
                  </div>

                  <div className="bg-white rounded-xl p-5 border border-gray-100 hover:border-gray-200 transition-colors">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Break Time</div>
                    </div>
                    <div className="text-xl font-bold text-gray-900">
                      {todayShift.custom_break_duration} min
                    </div>
                    <div className="text-sm text-gray-500">
                      Rest period
                    </div>
                  </div>

                  <div className="bg-white rounded-xl p-5 border border-gray-200 hover:border-gray-300 transition-colors">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Grace Period</div>
                    </div>
                    <div className="text-xl font-bold text-gray-900">
                      ±{todayShift.shift.grace_period_late} min
                    </div>
                    <div className="text-sm text-gray-500">
                      Early: {todayShift.shift.grace_period_early} min
                    </div>
                  </div>
                </div>

                {todayShift.shift.is_night_shift && (
                  <div className="mt-6 p-4 bg-gradient-to-r from-purple-100 to-indigo-100 border border-purple-200 rounded-xl">
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-semibold text-purple-900 mb-1">Night Shift Reminder</h4>
                        <p className="text-sm text-purple-800 mb-2">
                          This is a night shift - please ensure you're well-rested and have proper lighting for safety.
                        </p>
                        <div className="flex flex-wrap gap-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-200 text-purple-800">
                            🌙 Night Hours
                          </span>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-200 text-purple-800">
                            💡 Safety First
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DashboardCard>
      )}

      {/* All Assigned Shifts */}
      <DashboardCard title="All Shift Assignments" loading={isLoading}>
        {assignments.length === 0 ? (
          <div className="py-12 text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Shifts Assigned</h3>
            <p className="text-gray-500">You don't have any shifts assigned yet. Contact your manager for shift assignments.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {assignments.map((assignment) => (
              <div
                key={assignment.assignment_id}
                className={`border rounded-lg p-6 transition-all hover:border-gray-300 ${
                  assignment.is_active ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        assignment.shift.is_night_shift ? 'bg-purple-100' : 'bg-blue-100'
                      }`}>
                        <svg className={`w-5 h-5 ${
                          assignment.shift.is_night_shift ? 'text-purple-600' : 'text-blue-600'
                        }`} fill="currentColor" viewBox="0 0 20 20">
                          {assignment.shift.is_night_shift ? (
                            <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                          ) : (
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                          )}
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {assignment.shift.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {assignment.shift.description}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        {assignment.shift.is_night_shift && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Night Shift
                          </span>
                        )}
                        {assignment.shift.is_flexible && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Flexible
                          </span>
                        )}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          assignment.is_active
                            ? isActiveToday(assignment)
                              ? 'bg-green-100 text-green-800'
                              : 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {assignment.is_active
                            ? isActiveToday(assignment)
                              ? 'Active Today'
                              : 'Active'
                            : 'Inactive'}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Shift Hours</div>
                        <div className="text-sm font-semibold text-gray-900 mt-1">
                          {formatTime(assignment.custom_start_time)} - {formatTime(assignment.custom_end_time)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Duration: {calculateShiftDuration(
                            assignment.custom_start_time,
                            assignment.custom_end_time,
                            assignment.custom_break_duration
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Working Days</div>
                        <div className="text-sm font-semibold text-gray-900 mt-1">
                          {formatWorkingDays(assignment.custom_working_days)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Break: {assignment.custom_break_duration} minutes
                        </div>
                      </div>
                      <div>
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Period</div>
                        <div className="text-sm font-semibold text-gray-900 mt-1">
                          {formatDate(assignment.effective_start_date)}
                        </div>
                        <div className="text-xs text-gray-500">
                          to {formatDate(assignment.effective_end_date)}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Grace Period</div>
                        <div className="text-sm font-semibold text-gray-900 mt-1">
                          ±{assignment.shift.grace_period_late} minutes
                        </div>
                        <div className="text-xs text-gray-500">
                          Early: {assignment.shift.grace_period_early} min
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default EmployeeShiftsContent;
