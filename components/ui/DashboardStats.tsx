"use client";

import React from 'react';

interface DashboardStatsProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  className?: string;
  loading?: boolean;
}

const DashboardStats: React.FC<DashboardStatsProps> = ({
  title,
  value,
  change,
  changeType,
  className = '',
  loading = false,
}) => {
  const getChangeAriaLabel = () => {
    if (!change) return '';
    const direction = changeType === 'positive' ? 'increased' : changeType === 'negative' ? 'decreased' : 'changed';
    return `${direction} by ${change}`;
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-4 sm:p-6 hover:border-primary-300 transition-colors ${className}`}>
      {loading ? (
        <div className="flex flex-col items-center justify-center py-6 sm:py-8">
          <div
            className="animate-spin rounded-full h-6 w-6 border-2 border-gray-200 border-t-primary"
            role="status"
            aria-label="Loading statistics"
          ></div>
          <span className="mt-2 text-text-secondary text-sm font-medium">Loading...</span>
        </div>
      ) : (
        <>
          <div className="mb-3">
            <h3 className="text-sm font-medium text-text-secondary leading-tight">{title}</h3>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-end sm:justify-between gap-2">
            <div className="flex-1 min-w-0">
              <p className="text-2xl sm:text-3xl font-bold text-text-primary leading-none truncate" title={value}>
                {value}
              </p>
            </div>
            {change && (
              <div
                className={`inline-flex items-center text-xs font-semibold px-2 py-1 rounded-full flex-shrink-0 ${
                  changeType === 'positive'
                    ? 'text-success-600 bg-success-50'
                    : changeType === 'negative'
                    ? 'text-danger-600 bg-danger-50'
                    : 'text-text-secondary bg-gray-100'
                }`}
                aria-label={getChangeAriaLabel()}
              >
                {changeType !== 'neutral' && (
                  <svg
                    className={`h-3 w-3 mr-1 flex-shrink-0 ${changeType === 'negative' ? 'transform rotate-180' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                )}
                <span className="whitespace-nowrap">{change}</span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardStats;