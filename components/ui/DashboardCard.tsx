import React from 'react';

interface DashboardCardProps {
  children: React.ReactNode;
  title?: string | React.ReactNode;
  className?: string;
  loading?: boolean;
  action?: React.ReactNode;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  children,
  title,
  className = '',
  loading = false,
  action
}) => {
  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden hover:border-primary-300 transition-colors ${className}`}>
      {title && (
        <div className="px-4 sm:px-6 py-4 sm:py-5 border-b border-gray-100 bg-gray-50/50">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            {typeof title === 'string' ? (
              <h3 className="text-lg font-semibold text-text-primary leading-tight">{title}</h3>
            ) : (
              <div className="text-lg font-semibold text-text-primary leading-tight">{title}</div>
            )}
            {action && (
              <div className="flex-shrink-0">
                {action}
              </div>
            )}
          </div>
        </div>
      )}
      <div className="p-4 sm:p-6">
        {loading ? (
          <div className="flex flex-col items-center justify-center py-8 sm:py-12">
            <div
              className="animate-spin rounded-full h-8 w-8 border-2 border-gray-200 border-t-primary"
              role="status"
              aria-label="Loading"
            ></div>
            <span className="mt-3 text-text-secondary font-medium text-sm">Loading...</span>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default DashboardCard;